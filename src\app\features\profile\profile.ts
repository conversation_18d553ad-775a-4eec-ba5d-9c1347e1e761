import { Component, OnInit } from '@angular/core';
import { AuthService, UserProfile } from '../../core/services/auth.service';
import { GameService } from '../../core/services/game.service';
import { Router } from '@angular/router';
import { Game } from '../../core/models/game.model';

@Component({
  selector: 'app-profile',
  standalone: false,
  templateUrl: './profile.html',
  styleUrl: './profile.css'
})
export class Profile implements OnInit {
  userProfile: UserProfile | null = null;
  isLoading = true;
  errorMessage = '';

  // Admin section management
  activeSection: 'profile' | 'users' | 'games' = 'profile';

  // Games management
  games: Game[] = [];
  gamesLoading = false;
  gamesError = '';

  // Users management (placeholder for future API)
  users: any[] = [];
  usersLoading = false;
  usersError = '';

  constructor(
    private authService: AuthService,
    private gameService: GameService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadUserProfile();
  }

  loadUserProfile(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.authService.getUserProfile().subscribe({
      next: (profile) => {
        this.userProfile = profile;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to load profile';
        this.isLoading = false;
      }
    });
  }

  onLogout(): void {
    this.authService.logout();
  }

  refreshProfile(): void {
    this.loadUserProfile();
  }

  verifyToken(): void {
    this.authService.verifyToken().subscribe({
      next: (response) => {
        console.log('Token verified:', response);
        alert('Token is valid!');
      },
      error: (error) => {
        console.error('Token verification failed:', error);
        alert('Token verification failed: ' + error.message);
      }
    });
  }

  // Admin section management
  setActiveSection(section: 'profile' | 'users' | 'games'): void {
    this.activeSection = section;

    if (section === 'games' && this.games.length === 0) {
      this.loadGames();
    } else if (section === 'users' && this.users.length === 0) {
      this.loadUsers();
    }
  }

  // Games management methods
  loadGames(): void {
    this.gamesLoading = true;
    this.gamesError = '';

    this.gameService.getGames().subscribe({
      next: (games) => {
        this.games = games;
        this.gamesLoading = false;
      },
      error: (error) => {
        this.gamesError = error.message || 'Failed to load games';
        this.gamesLoading = false;
      }
    });
  }

  deleteGame(gameId: number): void {
    if (confirm('Вы уверены, что хотите удалить эту игру?')) {
      this.gameService.deleteGame(gameId).subscribe({
        next: () => {
          this.games = this.games.filter(game => game.id !== gameId);
          alert('Игра успешно удалена');
        },
        error: (error) => {
          alert('Ошибка при удалении игры: ' + error.message);
        }
      });
    }
  }

  // Users management methods (placeholder for future API)
  loadUsers(): void {
    this.usersLoading = true;
    this.usersError = '';

    // Placeholder - will be implemented when user management API is available
    setTimeout(() => {
      this.users = [
        { id: 1, email: '<EMAIL>', is_staff: true, created_at: '2024-01-01' },
        { id: 2, email: '<EMAIL>', is_staff: false, created_at: '2024-01-02' }
      ];
      this.usersLoading = false;
    }, 1000);
  }
}
