<!-- Profile Page Container -->
<div class="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 relative overflow-hidden">
  <!-- Animated background elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <!-- Floating geometric shapes -->
    <div class="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse"></div>
    <div class="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-blue-400/20 to-cyan-400/20 rounded-full blur-lg animate-bounce"></div>
    <div class="absolute bottom-32 left-1/4 w-40 h-40 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 rounded-full blur-2xl animate-pulse"></div>
    
    <!-- Floating network nodes -->
    <div class="absolute top-1/3 left-1/3 w-2 h-2 bg-blue-400 rounded-full animate-ping"></div>
    <div class="absolute top-1/2 right-1/3 w-2 h-2 bg-purple-400 rounded-full animate-ping" style="animation-delay: 1s;"></div>
    <div class="absolute bottom-1/3 left-1/2 w-2 h-2 bg-pink-400 rounded-full animate-ping" style="animation-delay: 2s;"></div>
  </div>

  <!-- Main Content Container -->
  <div class="relative z-10 flex min-h-screen">
    <!-- Left Sidebar -->
    <div class="w-80 bg-gradient-to-b from-purple-800/90 to-purple-900/90 backdrop-blur-sm border-r border-purple-500/30 flex flex-col">
      <!-- Logo Section -->
      <div class="p-6 border-b border-purple-500/30">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-lg">T</span>
          </div>
          <div>
            <h1 class="text-white font-bold text-xl">TOY FOR TOI</h1>
            <p class="text-purple-300 text-sm">Игровая платформа</p>
          </div>
        </div>
      </div>

      <!-- Navigation Menu -->
      <div class="flex-1 p-6 space-y-8">
        <!-- My Games Section -->
        <div class="mb-8">
          <h2 class="text-white font-semibold text-lg mb-4">Мои игры</h2>
          <div class="space-y-2">
            <a href="#" class="block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-purple-600/50">
              Библиотека
            </a>
            <a href="#" class="block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-purple-600/50">
              Загрузки
            </a>
          </div>
        </div>

        <!-- Account Section -->
        <div class="mb-8">
          <h2 class="text-white font-semibold text-lg mb-4">Учетная запись</h2>
          <div class="space-y-2">
            <div (click)="setActiveSection('profile')" [ngClass]="{'bg-purple-600/70': activeSection === 'profile'}" class="w-full text-left block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-purple-600/50 cursor-pointer">
              Параметры профиля
            </div>
            <a href="#" class="block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-purple-600/50">
              Пароль и безопасность
            </a>
          </div>
        </div>

        <!-- Store Section -->
        <div class="mb-8">
          <h2 class="text-white font-semibold text-lg mb-4">Магазин</h2>
          <div class="space-y-2">
            <a href="#" class="block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-purple-600/50">
              Каталог игр
            </a>
            <a href="#" class="block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-purple-600/50">
              Корзина
            </a>
            <a href="#" class="block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-purple-600/50">
              История покупок
            </a>
          </div>
        </div>

        <!-- Administration Section (Only for Staff) -->
        <div *ngIf="userProfile?.is_staff" class="mb-8">
          <h2 class="text-white font-semibold text-lg mb-4">Администрирование</h2>
          <div class="space-y-2">
            <div (click)="setActiveSection('users')" [ngClass]="{'bg-purple-600/70': activeSection === 'users'}" class="w-full text-left block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-purple-600/50 cursor-pointer">
              Управление пользователями
            </div>
            <div (click)="setActiveSection('games')" [ngClass]="{'bg-purple-600/70': activeSection === 'games'}" class="w-full text-left block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-purple-600/50 cursor-pointer">
              Управление играми
            </div>
          </div>
        </div>
      </div>

      <!-- Logout Button -->
      <div class="p-6 border-t border-purple-500/30">
        <button (click)="onLogout()" class="w-full bg-gradient-to-r from-red-600 to-red-700 text-white font-medium py-3 px-4 rounded-lg shadow-lg hover:from-red-700 hover:to-red-800 transition-all transform hover:scale-[1.02] profile-button">
          <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
          </svg>
          Выйти
        </button>
      </div>
    </div>

    <!-- Right Content Area -->
    <div class="flex-1 p-8 overflow-y-auto">
      <!-- Loading State -->
      <app-loading-spinner
        *ngIf="isLoading"
        [overlay]="true">
      </app-loading-spinner>

      <!-- Error State -->
      <div *ngIf="errorMessage && !isLoading" class="flex items-center justify-center h-64">
        <div class="text-center">
          <div class="bg-red-500/20 border border-red-500/50 rounded-lg p-6 max-w-md">
            <h3 class="text-red-300 font-semibold mb-2">Ошибка загрузки</h3>
            <p class="text-red-200 mb-4">{{ errorMessage }}</p>
            <button (click)="loadUserProfile()" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
              Попробовать снова
            </button>
          </div>
        </div>
      </div>

      <!-- Profile Content -->
      <div *ngIf="userProfile && !isLoading" class="max-w-6xl">
        <!-- Header with User Icon and Title -->
        <div class="flex items-center mb-8">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-6">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div>
            <h1 class="text-4xl font-bold text-white mb-2" *ngIf="activeSection === 'profile'">Параметры</h1>
            <h1 class="text-4xl font-bold text-white mb-2" *ngIf="activeSection === 'users'">Управление пользователями</h1>
            <h1 class="text-4xl font-bold text-white mb-2" *ngIf="activeSection === 'games'">Управление играми</h1>
            <p class="text-gray-300" *ngIf="activeSection === 'profile'">Управляйте данными своей учетной записи</p>
            <p class="text-gray-300" *ngIf="activeSection === 'users'">Просмотр и управление пользователями системы</p>
            <p class="text-gray-300" *ngIf="activeSection === 'games'">Создание, редактирование и удаление игр</p>
          </div>
        </div>

        <!-- Profile Section -->
        <div *ngIf="activeSection === 'profile'">
          <!-- Account Information Section -->
          <div class="bg-black/20 backdrop-blur-sm border border-gray-600/30 rounded-lg p-8 mb-8 profile-section">
            <h3 class="text-lg font-semibold text-white mb-6">Информация об аккаунте</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Email Field -->
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Email</label>
                <div class="relative">
                  <input type="email" [value]="userProfile.email" readonly class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent profile-input" />
                  <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                </div>
              </div>

              <!-- Password Field -->
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Пароль</label>
                <div class="relative">
                  <input type="password" value="••••••••" readonly class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent profile-input" />
                  <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                  </div>
                </div>
              </div>

              <!-- Phone Field -->
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Телефон</label>
                <div class="relative">
                  <input type="tel" placeholder="+7 (___) ___-__-__" class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent profile-input" />
                  <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-wrap gap-4 mt-8">
              <button class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg shadow-lg hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-[1.02] profile-button">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Обновить профиль
              </button>
              <button (click)="refreshProfile()" class="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg shadow-lg hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-[1.02] profile-button">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Обновить данные
              </button>
              <button (click)="verifyToken()" class="px-6 py-3 bg-gradient-to-r from-yellow-600 to-orange-600 text-white font-medium rounded-lg shadow-lg hover:from-yellow-700 hover:to-orange-700 transition-all transform hover:scale-[1.02] profile-button">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Проверить токен
              </button>
            </div>
          </div>

          <!-- User Status Info -->
          <div class="bg-black/20 backdrop-blur-sm border border-gray-600/30 rounded-lg p-6 profile-section">
            <h3 class="text-lg font-semibold text-white mb-4">Статус аккаунта</h3>
            <div class="space-y-3">
              <div class="flex justify-between items-center">
                <span class="text-gray-300">ID пользователя:</span>
                <span class="text-white font-medium">{{ userProfile.id }}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-300">Статус:</span>
                <span [ngClass]="userProfile.is_staff ? 'text-green-400' : 'text-blue-400'" class="font-medium">
                  {{ userProfile.is_staff ? 'Администратор' : 'Пользователь' }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Users Management Section -->
        <div *ngIf="activeSection === 'users'">
          <div class="bg-black/20 backdrop-blur-sm border border-gray-600/30 rounded-lg p-8 mb-8 profile-section">
            <h3 class="text-lg font-semibold text-white mb-6">Список пользователей</h3>

            <!-- Loading State -->
            <app-loading-spinner
              *ngIf="usersLoading"
              size="medium">
            </app-loading-spinner>

            <!-- Users List -->
            <div *ngIf="!usersLoading" class="space-y-4">
              <div *ngFor="let user of users" class="bg-gray-700/30 border border-gray-600/50 rounded-lg p-4">
                <div class="flex justify-between items-center">
                  <div>
                    <h4 class="text-white font-medium">{{ user.email }}</h4>
                    <p class="text-gray-400 text-sm">ID: {{ user.id }}</p>
                    <span [ngClass]="user.is_staff ? 'text-green-400' : 'text-blue-400'" class="text-sm font-medium">
                      {{ user.is_staff ? 'Администратор' : 'Пользователь' }}
                    </span>
                  </div>
                  <div class="flex space-x-2">
                    <button class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors">
                      Редактировать
                    </button>
                    <button class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors">
                      Удалить
                    </button>
                  </div>
                </div>
              </div>

              <div *ngIf="users.length === 0" class="text-center py-8 text-gray-400">
                Пользователи не найдены
              </div>
            </div>
          </div>
        </div>

        <!-- Games Management Section -->
        <div *ngIf="activeSection === 'games'">
          <div class="bg-black/20 backdrop-blur-sm border border-gray-600/30 rounded-lg p-8 mb-8 profile-section">
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold text-white">Управление играми</h3>
              <button class="px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg shadow-lg hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-[1.02]">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Добавить игру
              </button>
            </div>

            <!-- Loading State -->
            <app-loading-spinner
              *ngIf="gamesLoading"
              size="medium">
            </app-loading-spinner>

            <!-- Error State -->
            <div *ngIf="gamesError && !gamesLoading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-6">
              <h4 class="text-red-300 font-semibold mb-2">Ошибка загрузки игр</h4>
              <p class="text-red-200 mb-4">{{ gamesError }}</p>
              <button (click)="loadGames()" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                Попробовать снова
              </button>
            </div>

            <!-- Games List -->
            <div *ngIf="!gamesLoading && !gamesError" class="space-y-4">
              <div *ngFor="let game of games" class="bg-gray-700/30 border border-gray-600/50 rounded-lg p-6">
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <h4 class="text-white font-semibold text-lg mb-2">{{ game.title }}</h4>
                    <p class="text-gray-300 mb-3">{{ game.description }}</p>
                    <div class="flex flex-wrap gap-4 text-sm text-gray-400">
                      <span>Цена: <span class="text-green-400 font-medium">${{ game.price }}</span></span>
                      <span>Пробная версия: <span [ngClass]="game.trial_available ? 'text-green-400' : 'text-red-400'">{{ game.trial_available ? 'Доступна' : 'Недоступна' }}</span></span>
                      <span>Создано: <span class="text-blue-400">{{ game.created_at | date:'short' }}</span></span>
                    </div>
                  </div>
                  <div class="flex space-x-2 ml-4">
                    <button class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors">
                      Редактировать
                    </button>
                    <button (click)="deleteGame(game.id)" class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors">
                      Удалить
                    </button>
                  </div>
                </div>
              </div>

              <div *ngIf="games.length === 0" class="text-center py-8 text-gray-400">
                Игры не найдены
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
